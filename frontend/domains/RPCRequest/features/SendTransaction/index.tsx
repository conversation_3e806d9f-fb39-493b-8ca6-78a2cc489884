import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource } from '@zeal/domains/Main'
import {
    Network,
    NetworkHexId,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { FetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { FetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'
import { keystoreToUserEventType, RPCType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { SendRegularTransaction } from './SendRegularTransaction'
import { SendSafe4337Transaction } from './SendSafe4337Transaction'

type Props = {
    network: Network
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    portfolio: ServerPortfolio2 | null
    sendTransactionRequests: NonEmptyArray<EthSendTransaction>
    networksToSponsor?: NetworkHexId[]

    account: Account

    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string

    fetchSimulationByRequest: FetchSimulationByRequest
    fetchTransactionResultByRequest: FetchTransactionResultByRequest

    state: State

    actionSource: ActionSource

    defaultCurrencyConfig: DefaultCurrencyConfig

    onMsg: (msg: Msg) => void
}

export type State = { type: 'minimised' } | { type: 'maximised' }

type Msg =
    | MsgOf<typeof SendSafe4337Transaction>
    | MsgOf<typeof SendRegularTransaction>

const getRpcType = (keyStore: KeyStore, _installationId: string): RPCType => {
    switch (keyStore.type) {
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return 'rpc'
        case 'safe_4337':
            return 'biconomyBundler'
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}

export const SendTransaction = ({
    portfolio,
    network,
    networksToSponsor = [GNOSIS.hexChainId],
    networkRPCMap,
    sessionPassword,
    account,
    accounts,
    feePresetMap,
    gasCurrencyPresetMap,
    fetchSimulationByRequest,
    fetchTransactionResultByRequest,
    installationId,
    keystores,
    networkMap,
    sendTransactionRequests,
    state,
    actionSource,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const keyStore = getKeyStore({
        address: account.address,
        keyStoreMap: keystores,
    })
    const keyStoreLive = useLiveRef(keyStore)

    useEffect(() => {
        postUserEvent({
            type: 'TransactionRequestedEvent',
            installationId,
            keystoreType: keystoreToUserEventType(keyStoreLive.current),
            network: network.hexChainId,
            source: actionSource.transactionEventSource,
            keystoreId: keyStoreLive.current.id,
            rpcType: getRpcType(keyStoreLive.current, installationId),
        })
    }, [
        installationId,
        keyStoreLive,
        network.hexChainId,
        actionSource.transactionEventSource,
    ])

    switch (keyStore.type) {
        case 'safe_4337':
            return (
                <SendSafe4337Transaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networksToSponsor={networksToSponsor}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    installationId={installationId}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    state={state}
                    accountsMap={accounts}
                    keyStoreMap={keystores}
                    networkMap={networkMap}
                    account={account}
                    keyStore={keyStore}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    portfolio={portfolio}
                    rpcRequestsToBundle={sendTransactionRequests}
                    sessionPassword={sessionPassword}
                    actionSource={actionSource}
                    onMsg={onMsg}
                />
            )
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only':
            return (
                <SendRegularTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    account={account}
                    accounts={accounts}
                    feePresetMap={feePresetMap}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    installationId={installationId}
                    keystores={keystores}
                    network={network}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sendTransactionRequest={sendTransactionRequests[0]}
                    sessionPassword={sessionPassword}
                    state={state}
                    actionSource={actionSource}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(keyStore)
    }
}
