import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { AccountsMap } from '@zeal/domains/Account'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SubmittedToBundlerUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { SubmittedUserOperationBundled } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { getBundleTrxHash } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/createUserOperationFailedError'
import { fetchSubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/fetchSubmittedUserOperation'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { FetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import { GasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

import { Layout } from './Layout'

type Props = {
    userOperationRequest: SubmittedToBundlerUserOperationRequest
    simulation: SimulateTransactionResponse
    selectedFee: GasAbstractionTransactionFee
    accountsMap: AccountsMap
    installationId: string
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    visualState: VisualState
    actionSource: ActionSource
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    fetchTransactionResultByRequest: FetchTransactionResultByRequest
    onMsg: (msg: Msg) => void
}

export type VisualState = { type: 'minimised' } | { type: 'maximised' }

type Msg =
    | { type: 'on_safe_transaction_failure_accepted' }
    | {
          type: 'on_user_operation_bundled'
          userOperation: SubmittedUserOperationBundled
      }
    | MsgOf<typeof Layout>

const POLL_INTERVAL_MS = 1000

export const MonitorUserOperation = ({
    userOperationRequest,
    simulation,
    accountsMap,
    keyStoreMap,
    networkMap,
    selectedFee,
    installationId,
    fetchTransactionResultByRequest,
    visualState,
    actionSource,
    networkRPCMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const captureErrorOnce = useCaptureErrorOnce()
    const [pollable] = usePollableData(
        fetchSubmittedUserOperation,
        {
            type: 'loading',
            params: {
                submittedUserOperation:
                    userOperationRequest.submittedUserOperation,
                network: userOperationRequest.network,
                networkRPCMap,
            },
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
            stopIf: (pollable) => {
                switch (pollable.type) {
                    case 'loaded': {
                        switch (pollable.data.state) {
                            case 'pending':
                            case 'bundled':
                                return false
                            case 'completed':
                            case 'rejected':
                            case 'failed':
                                return true

                            default:
                                return notReachable(pollable.data)
                        }
                    }

                    case 'reloading':
                    case 'subsequent_failed':
                    case 'loading':
                    case 'error':
                        return false

                    default:
                        return notReachable(pollable)
                }
            },
        }
    )

    const onMsgLive = useLiveRef(onMsg)
    const actionSourceLive = useLiveRef(actionSource)

    useEffect(() => {
        switch (pollable.type) {
            case 'loading':
            case 'reloading':
                break
            case 'loaded':
                switch (pollable.data.state) {
                    case 'pending':
                        break
                    case 'completed':
                        postUserEvent({
                            type: 'TransactionCompletedEvent',
                            installationId,
                            keystoreType: 'Safe',
                            network: pollable.params.network.hexChainId,
                            source: actionSourceLive.current
                                .transactionEventSource,
                        })
                        break
                    case 'bundled':
                        onMsgLive.current({
                            type: 'on_user_operation_bundled',
                            userOperation: pollable.data,
                        })
                        break

                    case 'rejected':
                    case 'failed':
                        postUserEvent({
                            type: 'TransactionFailedEvent',
                            installationId,
                            transactionHash: getBundleTrxHash({
                                userOperation: pollable.data,
                            }),
                            keystoreType: 'Safe',
                            network: pollable.params.network.hexChainId,
                            state: pollable.data.state,
                            source: actionSourceLive.current
                                .transactionEventSource,
                        })
                        break

                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable.data)
                }
                break
            case 'subsequent_failed':
            case 'error':
                captureErrorOnce(pollable.error)
                break

            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [
        onMsgLive,
        pollable,
        userOperationRequest,
        captureErrorOnce,
        installationId,
        actionSourceLive,
    ])

    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            return (
                <Layout
                    networkRPCMap={networkRPCMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedFee={selectedFee}
                    installationId={installationId}
                    userOperationRequest={userOperationRequest}
                    submittedUserOperation={pollable.data}
                    simulation={simulation}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    visualState={visualState}
                    actionSource={actionSource}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    onMsg={onMsg}
                />
            )
        }

        case 'loading':
        case 'error':
            return (
                <Layout
                    networkRPCMap={networkRPCMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedFee={selectedFee}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    installationId={installationId}
                    userOperationRequest={userOperationRequest}
                    submittedUserOperation={
                        pollable.params.submittedUserOperation
                    }
                    simulation={simulation}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    visualState={visualState}
                    actionSource={actionSource}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(pollable)
    }
}
