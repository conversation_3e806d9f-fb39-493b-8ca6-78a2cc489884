import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Badge, DoubleAvatar } from '@zeal/uikit/Avatar'
import { Clock } from '@zeal/uikit/Icon/Clock'
import { SolidGift } from '@zeal/uikit/Icon/SolidGift'
import { ListItem } from '@zeal/uikit/ListItem'

import { notReachable } from '@zeal/toolkit'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { TakerPortfolio, TakerType } from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { KeyStore } from '@zeal/domains/KeyStore'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { fetchTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransaction'
import {
    BRewardClaimTransactionActivity,
    PendingARewardClaimTransactionActivity,
} from '@zeal/domains/Transactions'
import { PendingListItemSubtitle } from '@zeal/domains/Transactions/components/PendingListItemSubtitle'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    ownerAddress: Web3.address.Address
    takerType: TakerType
    takerPortfolio: TakerPortfolio
    pendingARewardClaimTransactionActivity: PendingARewardClaimTransactionActivity
    networkRPCMap: NetworkRPCMap
    installationId: string
    keyStore: KeyStore
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_pending_areward_claim_transaction_activity_completed'
          ownerAddress: Web3.address.Address
          pendingARewardClaimTransactionActivity: PendingARewardClaimTransactionActivity
          completedTransactionActivity: BRewardClaimTransactionActivity
      }
    | {
          type: 'on_pending_areward_claim_transaction_activity_failed'
          ownerAddress: Web3.address.Address
          pendingARewardClaimTransactionActivity: PendingARewardClaimTransactionActivity
      }

const POLL_INTERVAL_MILLISECONDS = 1000

export const Layout = ({
    ownerAddress,
    pendingARewardClaimTransactionActivity,
    networkRPCMap,
    installationId,
    keyStore,
    takerPortfolio,
    takerType,
    onMsg,
}: Props) => {
    const keystoreType = keystoreToUserEventType(keyStore)
    const onLiveMsg = useLiveRef(onMsg)

    const [pollable] = useLoadedPollableData(
        fetchTransaction,
        {
            type: 'reloading',
            params: {
                transaction:
                    pendingARewardClaimTransactionActivity.submittedTransaction,
                network: EARN_NETWORK,
                networkRPCMap,
            },
            data: pendingARewardClaimTransactionActivity.submittedTransaction,
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MILLISECONDS,
            stopIf: (loadable) => {
                switch (loadable.type) {
                    case 'loaded': {
                        switch (loadable.data.state) {
                            case 'queued':
                            case 'included_in_block':
                                return false
                            case 'completed':
                            case 'failed':
                            case 'replaced':
                                return true

                            default:
                                return notReachable(loadable.data)
                        }
                    }
                    case 'reloading':
                    case 'subsequent_failed':
                        return false

                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable)
                }
            },
        }
    )

    const amountInUserCurrency = applyRate2({
        baseAmount:
            pendingARewardClaimTransactionActivity.amountInTakerInvestmentCurrency,
        rate: takerPortfolio.userCurrencyRate,
    })

    const amountInDefaultCurrency =
        takerPortfolio.userCurrencyToDefaultCurrencyRate
            ? applyRate2({
                  baseAmount: amountInUserCurrency,
                  rate: takerPortfolio.userCurrencyToDefaultCurrencyRate,
              })
            : null

    const livePendignARewardClaimTransactionActivity = useLiveRef(
        pendingARewardClaimTransactionActivity
    )
    const liveAmountInUserCurrency = useLiveRef(amountInUserCurrency)

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded': {
                switch (pollable.data.state) {
                    case 'queued':
                    case 'included_in_block':
                        break
                    case 'completed':
                        postUserEvent({
                            type: 'TransactionCompletedEvent',
                            installationId,
                            source: 'claim_reward',
                            network: EARN_NETWORK.hexChainId,
                            keystoreType,
                        })
                        onLiveMsg.current({
                            type: 'on_pending_areward_claim_transaction_activity_completed',
                            ownerAddress,
                            pendingARewardClaimTransactionActivity:
                                livePendignARewardClaimTransactionActivity.current,
                            completedTransactionActivity: {
                                type: 'breward_claim',
                                hash: pollable.data.hash,
                                takerType,
                                toAmount:
                                    livePendignARewardClaimTransactionActivity
                                        .current
                                        .amountInTakerInvestmentCurrency,
                                toAmountInUserCurrency:
                                    liveAmountInUserCurrency.current,
                                fromAmount: null,
                                paidFee: null,
                                networkHexId: EARN_NETWORK.hexChainId,
                                timestamp: new Date(pollable.data.completedAt),
                            },
                        })
                        break
                    case 'failed':
                        postUserEvent({
                            type: 'TransactionFailedEvent',
                            installationId,
                            transactionHash: pollable.data.hash,
                            source: 'claim_reward',
                            network: EARN_NETWORK.hexChainId,
                            keystoreType,
                            state: 'failed',
                        })
                        onLiveMsg.current({
                            type: 'on_pending_areward_claim_transaction_activity_failed',
                            ownerAddress,
                            pendingARewardClaimTransactionActivity:
                                livePendignARewardClaimTransactionActivity.current,
                        })
                        break
                    case 'replaced':
                        onLiveMsg.current({
                            type: 'on_pending_areward_claim_transaction_activity_failed',
                            ownerAddress,
                            pendingARewardClaimTransactionActivity:
                                livePendignARewardClaimTransactionActivity.current,
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        notReachable(pollable.data)
                }
                break
            }
            case 'reloading':
                break
            case 'subsequent_failed':
                captureError(pollable.error)
                break
            /* istanbul ignore next */
            default:
                notReachable(pollable)
        }
    }, [
        onLiveMsg,
        liveAmountInUserCurrency,
        livePendignARewardClaimTransactionActivity,
        pollable,
        keystoreType,
        installationId,
        ownerAddress,
        takerType,
    ])

    return (
        <ListItem
            aria-current={false}
            size="regular"
            avatar={() => (
                <DoubleAvatar
                    badge={({ size }) => (
                        <Badge
                            outlineColor="surfaceDefault"
                            backgroundColor="surfaceDefault"
                            size={size}
                        >
                            <Clock size={size} color="iconStatusNeutral" />
                        </Badge>
                    )}
                    back={({ size }) => (
                        <SolidGift size={size} color="gray20" />
                    )}
                    front={({ size, border }) => (
                        <TakerAvatar
                            size={size}
                            border={border}
                            vairiant="rounded"
                            takerType={takerType}
                        />
                    )}
                />
            )}
            primaryText={
                <FormattedMessage
                    id="activity.title.pendidng_areward_claim"
                    defaultMessage="Claiming reward"
                />
            }
            shortText={
                <PendingListItemSubtitle
                    transaction={pendingARewardClaimTransactionActivity}
                />
            }
            side={{
                title: (
                    <FormattedMoneyPrecise
                        withSymbol
                        money={amountInUserCurrency}
                        sign="+"
                    />
                ),
                subtitle: amountInDefaultCurrency ? (
                    <FormattedMoneyPrecise
                        withSymbol
                        money={amountInDefaultCurrency}
                        sign="+"
                    />
                ) : null,
            }}
        />
    )
}
