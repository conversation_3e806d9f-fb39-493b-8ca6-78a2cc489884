import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { createEOATransactionFailedError } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/createEOATransactionFailedError'
import { submitAndMonitorTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/submitAndMonitorTransaction'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { CardTopUpEOAQuote } from '../../../../types'
import { AppErrorPopupWithHardwareWalletErrors } from '../AppErrorPopupWithHardwareWalletErrors'
import { LoadingLayout } from '../LoadingLayout'

type Props = {
    quote: Extract<CardTopUpEOAQuote, { type: 'earn_eoa_sign_typed_data' }>
    fromAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_withdrawal_transaction_completed' }

const fetch = async ({
    quote,
    fromAddress,
    sessionPassword,
    networkRPCMap,
    signal,
}: {
    quote: Extract<CardTopUpEOAQuote, { type: 'earn_eoa_sign_typed_data' }>
    fromAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<void> => {
    const network = EARN_NETWORK

    const actionSource: InternalTransactionActionSource = {
        type: 'internal',
        transactionEventSource: 'topupCardEarnBungeeWithdraw',
    }

    const finishedTransaction = await submitAndMonitorTransaction({
        sendTransactionRequest: quote.withdrawalTransaction,
        nonce: quote.withdrawalFee.nonce,
        keyStore: quote.keystore,
        sessionPassword,
        network,
        networkRPCMap,
        fee: quote.withdrawalFee.fast,
        gas: getSuggestedGasLimit(fromBigInt(quote.withdrawalGasEstimate)),
        senderAddress: fromAddress,
        actionSource,
        signal,
    })

    switch (finishedTransaction.state) {
        case 'completed':
            return undefined
        case 'failed':
        case 'replaced':
            throw await createEOATransactionFailedError({
                actionSource,
                keyStore: quote.keystore,
                network,
                transaction: finishedTransaction,
            })
        /* istanbul ignore next */
        default:
            return notReachable(finishedTransaction)
    }
}

export const Withdrawal = ({
    quote,
    fromAddress,
    sessionPassword,
    networkRPCMap,
    networkMap,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            fromAddress,
            sessionPassword,
            networkRPCMap,
            networkMap,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_withdrawal_transaction_completed',
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_2" />
                    <AppErrorPopupWithHardwareWalletErrors
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'on_trezor_error_close':
                                case 'on_ledger_error_close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'on_sync_ledger_click':
                                case 'on_sync_trezor_click':
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAddress,
                                            sessionPassword,
                                            networkRPCMap,
                                            networkMap,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
