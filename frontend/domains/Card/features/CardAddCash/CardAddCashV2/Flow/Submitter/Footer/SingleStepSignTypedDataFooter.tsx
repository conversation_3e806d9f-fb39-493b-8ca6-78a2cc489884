import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { SubmittedSwapSignTypedDataCardTopUp } from '@zeal/domains/Card'
import { submitBungeeIntentQuote } from '@zeal/domains/Currency/domains/Bungee/api/submitBungeeIntentQuote'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { AppErrorPopupWithHardwareWalletErrors } from './AppErrorPopupWithHardwareWalletErrors'
import { LoadingLayout } from './LoadingLayout'

import { CardTopUpEOAQuote, CardTopUpSafeQuote } from '../../../types'

type Props = {
    quote:
        | Extract<CardTopUpEOAQuote, { type: 'swap_eoa_sign_typed_data' }>
        | Extract<CardTopUpSafeQuote, { type: 'swap_safe_sign_typed_data' }>
    fromAmount: CryptoMoney
    cardSafeAddress: Web3.address.Address

    networkMap: NetworkMap
    sessionPassword: string
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp: SubmittedSwapSignTypedDataCardTopUp
      }

const fetch = async ({
    quote,
    fromAmount,
    networkMap,
    cardSafeAddress,
    sessionPassword,
}: {
    quote:
        | Extract<CardTopUpEOAQuote, { type: 'swap_eoa_sign_typed_data' }>
        | Extract<CardTopUpSafeQuote, { type: 'swap_safe_sign_typed_data' }>
    cardSafeAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    fromAmount: CryptoMoney
}): Promise<SubmittedSwapSignTypedDataCardTopUp> => {
    const network = findNetworkByHexChainId(
        fromAmount.currency.networkHexChainId,
        networkMap
    )

    const signedMessage = await signMessage({
        sessionPassword,
        request: quote.swapSignatureRequest,
        keyStore: quote.keystore,
        network,
        dApp: null,
    })

    const requestHash = await submitBungeeIntentQuote({
        quoteId: quote.quoteId,
        quoteWiness: quote.witness,
        userSignature: signedMessage,
        quoteRequestType: quote.quoteRequestType,
    })

    return {
        type: 'swap_sign_typed_data',
        fromAmount,
        toAmount: quote.toAmount,
        quoteRequestHash: requestHash,
        keyStore: quote.keystore,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'topupCardBungee',
        },
        cardSafeAddress,
        startedAtMs: Date.now(),
        state: { type: 'waiting_for_swap' },
    }
}

export const SingleStepSignTypedDataFooter = ({
    fromAmount,
    sessionPassword,
    quote,
    networkMap,
    cardSafeAddress,
    onMsg,
    installationId,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            fromAmount,
            networkMap,
            cardSafeAddress,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_1" />

        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_1" />
                    <AppErrorPopupWithHardwareWalletErrors
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'on_trezor_error_close':
                                case 'on_ledger_error_close':
                                case 'close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'on_sync_trezor_click':
                                case 'on_sync_ledger_click':
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAmount,
                                            sessionPassword,
                                            cardSafeAddress,
                                            networkMap,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
