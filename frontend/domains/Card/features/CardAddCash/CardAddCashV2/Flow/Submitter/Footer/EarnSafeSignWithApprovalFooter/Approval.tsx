import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { createUserOperationFailedError } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/createUserOperationFailedError'
import { monitorSubmittedUserOperation } from '@zeal/domains/UserOperation/api/monitorSubmittedUserOperation'
import { sign as signUserOperation } from '@zeal/domains/UserOperation/api/sign'
import { submit } from '@zeal/domains/UserOperation/api/submit'

import { CardTopUpSafeQuote } from '../../../../types'
import { LoadingLayout } from '../LoadingLayout'

type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'earn_safe_sign_typed_data_with_optional_approval' }
    >
    installationId: string
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_approval_transaction_completed' } | { type: 'close' }

const fetch = async ({
    networkRPCMap,
    sessionPassword,
    signal,
    installationId,
    quote,
}: {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'earn_safe_sign_typed_data_with_optional_approval' }
    >
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    installationId: string
    signal?: AbortSignal
}): Promise<void> => {
    const network = EARN_NETWORK

    const userOperationWithSignature = await signUserOperation({
        fee: quote.fee,
        keyStore: quote.keystore,
        metaTransactionDatas: quote.metaTransactionDatas,
        network,
        entrypointNonce: quote.entrypointNonce,
        safeInstance: quote.safeInstance,
        sessionPassword,
        signal,
    })

    const actionSource: InternalTransactionActionSource = {
        type: 'internal',
        transactionEventSource:
            'topupCardEarnBungeeWithdrawWithOptionalApproval',
    }

    const submittedUserOperation = await submit({
        userOperationWithSignature,
        network,
        networkRPCMap,
        actionSource,
    })

    const finishedUserOperation = await monitorSubmittedUserOperation({
        installationId,
        network,
        networkRPCMap,
        submittedUserOperation,
        signal,
    })

    switch (finishedUserOperation.state) {
        case 'completed':
            return undefined
        case 'failed':
        case 'rejected':
            throw await createUserOperationFailedError({
                actionSource,
                network,
                userOperation: finishedUserOperation,
            })
        /* istanbul ignore next */
        default:
            return notReachable(finishedUserOperation)
    }
}

export const Approval = ({
    quote,
    sessionPassword,
    networkRPCMap,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            networkRPCMap,
            quote,
            installationId,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_approval_transaction_completed',
                })
                return
            case 'loading':
            case 'error':
                return

            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_2" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
