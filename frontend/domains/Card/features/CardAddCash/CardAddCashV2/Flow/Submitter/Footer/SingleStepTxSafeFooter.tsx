import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    SubmittedSafeEarnEUReCardTopUp,
    SubmittedSafeSendCardTopUp,
    SubmittedSafeSendNativeCardTopUp,
} from '@zeal/domains/Card'
import {
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP,
} from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { sign as signUserOperation } from '@zeal/domains/UserOperation/api/sign'
import { submit as submitUserOperation } from '@zeal/domains/UserOperation/api/submit'

import { LoadingLayout } from './LoadingLayout'

import { CardTopUpSafeQuote } from '../../../types'

type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        {
            type:
                | 'send_safe'
                | 'swap_safe_native_send_transaction'
                | 'earn_safe_eure_withdrawal'
        }
    >
    cardSafeAddress: Web3.address.Address
    fromAmount: CryptoMoney
    installationId: string
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp:
              | SubmittedSafeSendCardTopUp
              | SubmittedSafeSendNativeCardTopUp
              | SubmittedSafeEarnEUReCardTopUp
      }
    | { type: 'close' }

const submit = async ({
    quote,
    networkMap,
    networkRPCMap,
    cardSafeAddress,
    sessionPassword,
    fromAmount,
    signal,
}: {
    fromAmount: CryptoMoney
    quote: Extract<
        CardTopUpSafeQuote,
        {
            type:
                | 'send_safe'
                | 'swap_safe_native_send_transaction'
                | 'earn_safe_eure_withdrawal'
        }
    >
    cardSafeAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<
    | SubmittedSafeSendCardTopUp
    | SubmittedSafeSendNativeCardTopUp
    | SubmittedSafeEarnEUReCardTopUp
> => {
    switch (quote.type) {
        case 'send_safe': {
            const network = findNetworkByHexChainId(
                quote.toAmount.currency.networkHexChainId,
                networkMap
            )

            const actionSource: InternalTransactionActionSource = {
                type: 'internal',
                transactionEventSource: 'topupCardSend',
            }

            const userOperationWithSignature = await signUserOperation({
                fee: quote.fee,
                keyStore: quote.keystore,
                metaTransactionDatas: [quote.transaction],
                network,
                entrypointNonce: quote.entrypointNonce,
                safeInstance: quote.safeInstance,
                sessionPassword,
                signal,
            })

            const submittedUserOperation = await submitUserOperation({
                userOperationWithSignature,
                network,
                networkRPCMap,
                actionSource,
            })

            return {
                type: 'send_safe',
                amount: quote.toAmount,
                startedAtMs: submittedUserOperation.queuedAt,
                state: {
                    type: 'waiting_for_user_operation',
                    submittedUserOperation,
                },
                keyStore: quote.keystore,
                cardSafeAddress,
                actionSource,
            }
        }

        case 'earn_safe_eure_withdrawal': {
            const network = EARN_NETWORK

            const actionSource: InternalTransactionActionSource = {
                type: 'internal',
                transactionEventSource: 'topupCardEarnAAVEEURe',
            }

            const userOperationWithSignature = await signUserOperation({
                fee: quote.fee,
                keyStore: quote.keystore,
                metaTransactionDatas: [
                    quote.earnWithdrawalTransaction,
                    quote.aaveWithdrawalTransaction,
                ],
                network,
                entrypointNonce: quote.entrypointNonce,
                safeInstance: quote.safeInstance,
                sessionPassword,
                signal,
            })

            const submittedUserOperation = await submitUserOperation({
                userOperationWithSignature,
                network,
                networkRPCMap,
                actionSource,
            })

            const takerType =
                EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP[
                    fromAmount.currency.id
                ]

            if (!takerType) {
                throw new ImperativeError('Taker not found for currency', {
                    currencyId: fromAmount.currency.id,
                })
            }

            return {
                type: 'earn_safe_eure_withdrawal',
                takerType,
                amount: quote.toAmount,
                startedAtMs: submittedUserOperation.queuedAt,
                state: {
                    type: 'waiting_for_user_operation',
                    submittedUserOperation,
                },
                keyStore: quote.keystore,
                cardSafeAddress,
                actionSource,
            }
        }

        case 'swap_safe_native_send_transaction': {
            const network = findNetworkByHexChainId(
                fromAmount.currency.networkHexChainId,
                networkMap
            )

            const actionSource: InternalTransactionActionSource = {
                type: 'internal',
                transactionEventSource: 'topupCardBungeeNativeSend',
            }

            const userOperationWithSignature = await signUserOperation({
                fee: quote.fee,
                keyStore: quote.keystore,
                metaTransactionDatas: [quote.transaction],
                network,
                entrypointNonce: quote.entrypointNonce,
                safeInstance: quote.safeInstance,
                sessionPassword,
                signal,
            })

            const submittedUserOperation = await submitUserOperation({
                userOperationWithSignature,
                network,
                networkRPCMap,
                actionSource,
            })

            return {
                type: 'swap_safe_native_send_transaction',
                fromAmount,
                keyStore: quote.keystore,
                toAmount: quote.toAmount,
                quoteRequestHash: quote.quoteRequestHash,
                startedAtMs: submittedUserOperation.queuedAt,
                state: {
                    type: 'waiting_for_user_operation',
                    submittedUserOperation,
                },
                cardSafeAddress,
                actionSource,
            }
        }

        default:
            return notReachable(quote)
    }
}

export const SingleStepTxSafeFooter = ({
    quote,
    networkMap,
    networkRPCMap,
    sessionPassword,
    installationId,
    cardSafeAddress,
    fromAmount,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(submit, {
        type: 'loading',
        params: {
            sessionPassword,
            networkRPCMap,
            networkMap,
            fromAmount,
            cardSafeAddress,
            quote,
        },
    })
    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                break
            case 'loading':
            case 'error':
                break

            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_1" />

        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_1" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
