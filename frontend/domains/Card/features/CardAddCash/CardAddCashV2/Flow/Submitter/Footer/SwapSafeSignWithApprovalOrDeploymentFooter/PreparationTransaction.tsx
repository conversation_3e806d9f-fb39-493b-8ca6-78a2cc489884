import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { ActionSource } from '@zeal/domains/Main'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { createUserOperationFailedError } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/createUserOperationFailedError'
import { monitorSubmittedUserOperation } from '@zeal/domains/UserOperation/api/monitorSubmittedUserOperation'
import { sign as signUserOperation } from '@zeal/domains/UserOperation/api/sign'
import { submit } from '@zeal/domains/UserOperation/api/submit'

import { CardTopUpSafeQuote } from '../../../../types'
import { LoadingLayout } from '../LoadingLayout'

type Props = {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'swap_safe_sign_typed_data_with_approval_or_safe_deployment' }
    >
    sessionPassword: string
    installationId: string
    fromAmount: CryptoMoney
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_preparation_transaction_completed' } | { type: 'close' }

const fetch = async ({
    quote,
    networkMap,
    networkRPCMap,
    sessionPassword,
    fromAmount,
    installationId,
    signal,
}: {
    quote: Extract<
        CardTopUpSafeQuote,
        { type: 'swap_safe_sign_typed_data_with_approval_or_safe_deployment' }
    >
    fromAmount: CryptoMoney
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    signal?: AbortSignal
}): Promise<void> => {
    const network = findNetworkByHexChainId(
        fromAmount.currency.networkHexChainId,
        networkMap
    )

    const userOperationWithSignature = await signUserOperation({
        fee: quote.fee,
        keyStore: quote.keystore,
        metaTransactionDatas: [quote.preparationTransaction],
        network,
        entrypointNonce: quote.entrypointNonce,
        safeInstance: quote.safeInstance,
        sessionPassword,
        signal,
    })

    const actionSource: ActionSource = {
        type: 'internal',
        transactionEventSource: 'topupCardBungeeApprovalOrDeployment',
    }

    const submittedUserOperation = await submit({
        userOperationWithSignature,
        network,
        networkRPCMap,
        actionSource: {
            type: 'internal',
            transactionEventSource: 'topupCardBungeeApprovalOrDeployment',
        },
    })

    const finishedUserOperation = await monitorSubmittedUserOperation({
        installationId,
        network,
        networkRPCMap,
        submittedUserOperation,
        signal,
    })

    switch (finishedUserOperation.state) {
        case 'completed':
            return undefined

        case 'failed':
        case 'rejected':
            throw await createUserOperationFailedError({
                actionSource,
                network,
                userOperation: finishedUserOperation,
            })

        /* istanbul ignore next */
        default:
            return notReachable(finishedUserOperation)
    }
}

export const PreparationTransaction = ({
    quote,
    sessionPassword,
    networkRPCMap,
    networkMap,
    installationId,
    fromAmount,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            networkRPCMap,
            networkMap,
            quote,
            fromAmount,
            installationId,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_preparation_transaction_completed',
                })
                return
            case 'loading':
            case 'error':
                return

            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_1_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_1_of_2" />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
