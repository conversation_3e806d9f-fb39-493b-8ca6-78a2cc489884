import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { SubmittedEOAEarnEUReCardTopUp } from '@zeal/domains/Card'
import {
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP,
} from '@zeal/domains/Earn/constants'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { ActionSource } from '@zeal/domains/Main'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { signAndSubmitTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/signAndSubmitTransaction'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { CardTopUpEOAQuote } from '../../../../types'
import { AppErrorPopupWithHardwareWalletErrors } from '../AppErrorPopupWithHardwareWalletErrors'
import { LoadingLayout } from '../LoadingLayout'

type Props = {
    quote: Extract<CardTopUpEOAQuote, { type: 'earn_eoa_eure_withdrawal' }>
    fromAmount: CryptoMoney
    fromAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    cardSafeAddress: Web3.address.Address
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp: SubmittedEOAEarnEUReCardTopUp
      }

const fetch = async ({
    fromAddress,
    fromAmount,
    networkMap,
    cardSafeAddress,
    networkRPCMap,
    quote,
    sessionPassword,
    signal,
}: {
    quote: Extract<CardTopUpEOAQuote, { type: 'earn_eoa_eure_withdrawal' }>
    fromAddress: Web3.address.Address
    cardSafeAddress: Web3.address.Address
    fromAmount: CryptoMoney
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedEOAEarnEUReCardTopUp> => {
    const actionSource: ActionSource = {
        type: 'internal',
        transactionEventSource: 'topupCardEarnAAVEEURe',
    }

    const finishedTransaction = await signAndSubmitTransaction({
        sendTransactionRequest: quote.aaveWithdrawalTransaction,
        nonce: quote.aaveWithdrawalFee.nonce,
        keyStore: quote.keystore,
        sessionPassword,
        network: EARN_NETWORK,
        networkRPCMap,
        fee: quote.aaveWithdrawalFee.fast,
        gas: getSuggestedGasLimit(
            Hexadecimal.fromBigInt(quote.aaveWithdrawalGasEstimate)
        ),
        senderAddress: fromAddress,
        actionSource,
        signal,
    })

    const takerType =
        EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP[fromAmount.currency.id]

    if (!takerType) {
        throw new ImperativeError('Taker not found for currency', {
            currencyId: fromAmount.currency.id,
        })
    }

    return {
        type: 'earn_eoa_eure_withdrawal',
        takerType,
        amount: fromAmount,
        keyStore: quote.keystore,
        startedAtMs: Date.now(),
        actionSource,
        cardSafeAddress,
        state: {
            type: 'waiting_for_transaction',
            submittedTransaction: finishedTransaction,
        },
    }
}

export const AAVEWithdrawal = ({
    quote,
    fromAddress,
    sessionPassword,
    networkRPCMap,
    networkMap,
    installationId,
    cardSafeAddress,
    fromAmount,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            fromAmount,
            fromAddress,
            sessionPassword,
            cardSafeAddress,
            networkRPCMap,
            networkMap,
            quote,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_2_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_2_of_2" />
                    <AppErrorPopupWithHardwareWalletErrors
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'on_trezor_error_close':
                                case 'on_ledger_error_close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'on_sync_ledger_click':
                                case 'on_sync_trezor_click':
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAddress,
                                            sessionPassword,
                                            cardSafeAddress,
                                            networkRPCMap,
                                            networkMap,
                                            quote,
                                            fromAmount,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
